/*
Theme Name: Tema MEMBERO
Description:  Um tema exclusivo da MEMBERO para você.
Template:     astra
Version:      4.5
*/


@import url("../astra/style.css");

/* Estilos para o toggle switch do gerenciamento de cursos de usuário */
.ucc-profile-edit-form .ucc-switch {
    position: relative;
    display: inline-block;
    width: 60px;
    height: 34px;
}

.ucc-profile-edit-form .ucc-switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.ucc-profile-edit-form .ucc-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
}

.ucc-profile-edit-form .ucc-slider:before {
    position: absolute;
    content: "";
    height: 26px;
    width: 26px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: .4s;
}

.ucc-profile-edit-form .ucc-switch input:checked + .ucc-slider {
    background-color: #2196F3;
}

.ucc-profile-edit-form .ucc-switch input:checked + .ucc-slider:before {
    transform: translateX(26px);
}

.ucc-profile-edit-form .ucc-slider.ucc-round {
    border-radius: 34px;
}

.ucc-profile-edit-form .ucc-slider.ucc-round:before {
    border-radius: 50%;
}

.membero-pdf-container {
    position: relative;
    width: 100%;
    padding-top: 56.25%; /* Proporção 16:9 */
    overflow: hidden;
}

.membero-pdf-container iframe {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
}


#tutor-lms-overlay {
    z-index: 10; /* Garante que fique sobre o vídeo */
    pointer-events: none; /* Ignora cliques, garantindo que o usuário possa controlar o vídeo */
}

.hide-comments {
    display: none !important;
  }
  
  #course-settings {
      display: none;
  }
  
  /* Foto Perfil Usuário Menu */
  .iconeprofilemenu img{
    border: 2px solid var(--ast-global-color-0) !important;
}
    
  /* ---------------------------------- */
  /* PÁGINA MÓDULOS/AULAS */
  /* ---------------------------------- */

  .tutor-course-spotlight-wrapper .tutor-course-spotlight-tab{
    margin: 10px;
}
  
  #tutor-dashboard-footer-mobile{
      display: none !important;
  }
  
    /* Background nome do curso/módulo */
    .tutor-course-topic-single-header-title{
        margin-left: 0px;
        background-color: #333131;
        padding: 5px 20px 5px 20px;
        border-radius: 10px;
        font-weight: 600 !important;
      }


 
  
  /* Ocultação do Titulo do Menu de Aulas */
  .tutor-course-single-sidebar-title{
      display: none !important;
  }
  
  
  /* Espaçamento entre o cabeçalho e o conteúdo */
  .tutor-course-single-content-wrapper {
      padding-top: 14px !important;
  }
  
  .tutor-frontend-dashboard-header {
      display: none !important;
  }
  
  .tutor-dashboard-content .tutor-fs-5.tutor-fw-medium.tutor-mb-24
  {
      padding-left: 2% !important;
  }
  
  /* Icone tela toda */
  .tutor-course-topics-sidebar-toggler {
      display: none !important;
  }
  
  /* Botão Próximo e Anterior */
  .tutor-course-topic-single-footer {
      display: none !important;
  }
  
  /* Título Menu Módulos/Aula */
  .tutor-course-single-sidebar-title .tutor-fs-6.tutor-fw-medium.tutor-color-secondary {
      display: none;
  }
  
  /* Aba comentários no tamanho total */
  .tutor-course-spotlight-comments{
      max-width: 95%;
  }
  
  .tutor-course-spotlight-wrapper .tutor-conversation{
  border-bottom: none;
  }
  
  /* Barra Lateral */
  .tutor-course-single-sidebar-wrapper {
      flex: 0 0 400px;
      width: 400px;
      background-color: #141414;
      border: 1px solid rgba(255, 255, 255, 0.05);
      margin-top: 30px;
      margin-bottom: 30px;
          margin-right: 20px;
      margin-left: 0px;
      border-radius: 10px;
      padding-left: 20px;
          padding-right: 20px;
          padding-top: 30px;	
  }
  
  /* Barra Lateral - Conteúdo Curso - Titulo Principal */
  .tutor-course-single-sidebar-title {
      display: flex;
      align-items: center;
      padding: 8px 16px;
      height: 60px;
      background-color: #fff;
      border-bottom: 0px !important;
      border-radius: 10px;
      margin-bottom: 10px;
  }

    /* Ocultar Icone Youtube */
  .tutor-icon-brand-youtube-bold{
    display: none !important;
}

  /* Nome da aula - HML */
  .tutor-ml-xl-24 {
    margin-left: 0px;
    background-color: #333131;
    padding: 5px 20px 5px 20px;
    border-radius: 10px;
    font-weight: 600 !important;
}

.tutor-alert {
    background: none !important;
}

    /* Icone esquerdo nome da aula */  
.tutor-course-topic-item-icon {
    display:none;
    
}
  
  /* Espaçamento Branco */
  .tutor-course-single-sidebar-wrapper .tutor-accordion-item-body {
      background-color: #ffffff00 !important;
  }
  
  
  .tutor-course-single-sidebar-wrapper .tutor-accordion-item-body {
      background-color: none;
  }
  
  /* Backgorund Video */
  .tutor-course-single-content-wrapper .tutor-video-player .loading-spinner {
      background: #121212 !important;
  }
  
  
  .tutor-course-single-sidebar-wrapper .tutor-course-topic-item.is-active a{
      background-color: #333131 !important;
      border-radius: 10px;
  }
  
  .tutor-course-single-sidebar-wrapper .tutor-course-topic-item-icon {
      color: #fff !important;
  }
  
  .tutor-course-single-sidebar-wrapper .tutor-course-topic-item-title{
      color: #fff !important;
  }
  
  .tutor-course-single-sidebar-wrapper .tutor-accordion-item-header:after{
      color: #fff;
  }
  
  .tutor-course-single-sidebar-wrapper .tutor-accordion-item-header:after{
      color: #fff !important;
  }
  
  .tutor-course-single-sidebar-wrapper .tutor-course-topic-item a{
      background-color: #141414;
  }
  
  .tutor-course-single-sidebar-wrapper .tutor-accordion-item-header .tutor-course-topic-summary{
      color: #fff !important;
  }
  
  /* Barra Lateral - Conteúdo Curso - Titulo Curso */
  .tutor-course-single-sidebar-wrapper .tutor-accordion-item-header {
      font-size: 15px;
      font-weight: 600;
      color: var(--tutor-body-color);
      background-color: #6a6a6a !important;
     border-bottom: 0px !important;
      padding: 12px 44px 12px 16px;
      border-radius: 5px !important;
      user-select: none;
      outline: transparent solid 2px;
      outline-offset: 2px;
      cursor: pointer;
        margin-bottom: 5px !important;
  }

  /* Hover ao clicar na aula - HML*/
.tutor-course-single-sidebar-wrapper .tutor-course-topic-item a:hover {
    background-color: #333131 !important; /* Cor cinza ao passar o mouse */
    margin-top:5px;
    margin-bottom:5px;
    border-radius: 10px; /* Mantém o border-radius */
}
  
  .tutor-video-player-wrapper {
      margin-right: 20px;
          margin-left: 20px;
  }
  
  .plyr--video {
      background: #121212;
      overflow: hidden;   
  }

  .tutor-col-xl-8 {
    border-radius: 10px !important;
    padding: 30px 30px 30px 30px;
    outline: transparent solid 2px;
    border: 1px solid rgba(255, 255, 255, 0.05);
}
  
  .tutor-course-single-sidebar-title {
      background-color: #141414;
  
  }
  
  .tutor-course-topic-title{
      color: #fff;
  }
  
  .tutor-course-single-content-wrapper #tutor-single-entry-content .tutor-course-topic-single-header{
      background-color: #121212;
  }
  
  .tutor-course-single-content-wrapper #tutor-single-entry-content .tutor-course-topic-single-footer {
      border-top: #121212;
      background-color: #121212;
  }
  
  .tutor-btn-secondary {
      border-color: var(--ast-global-color-0);
      background-color: var(--ast-global-color-0);
      color: #121212;
      border-color: var(--ast-global-color-0);
  }
  
  .tutor-color-black {
      color: #fff;
  }
  
  .tutor-color-secondary {
      color: #fff;
  }
  
  .tutor-nav:not(.tutor-nav-pills):not(.tutor-nav-tabs) .tutor-nav-link {
      color: #fff;
  }
  .tutor-color-muted {
      color: #fff;
  }
  
  .tutor-course-content-list-item{
          background-color: #424242;
  }
  
  .tutor-nav:not(.tutor-nav-pills):not(.tutor-nav-tabs) {
      border-bottom: 0px;
  }
  
  
  /* ---------------------------------- */
  /* Comentário abaixo da aula */
  /* ---------------------------------- */
  div.tutor-comment-text.tutor-mt-4{
      color: #fff;
  }
  
  textarea.tutor-form-control {
      background: #1B1B24 !important;
      border: none !important;
  }
  
  .tutor-actual-comment{
      background: #1B1B24 !important;
      border: none !important;
  }
  
  .tutor-comment-author > span {
      color: #fff  !important; /* Altere para a cor desejada */
  }
  
  
  /*----------------------------*/
  
  .tutor-btn-primary:hover {
      border-color: var(--ast-global-color-1);
      background-color: var(--ast-global-color-1);
      color: #000;
      cursor: pointer; /* Cursor ao passar o mouse */
      transform: scale(0.9);
      transition: transform 0.3s ease; /* Transição suave da transformação durante 0.3 segundos */
  }
  
  
  .tutor-course-spotlight-wrapper .tutor-conversation .tutor-comment-box .tutor-comment-textarea {
      border: 0px;
  }
  
  /*----------------------------*/
  
  /* Cor Botoões Player Vídeo da Aulas */
  
  button.plyr__control:hover{
      color: #000 !important;
      background-color: var(--ast-global-color-1) !important;
  }
  
  
  /* Botão Player no meio do video */
  .plyr__control--overlaid {
      background: var(--ast-global-color-0) !important;
      border-color: #000;
  }
  
  .plyr--full-ui input[type=range] {
      color: var(--ast-global-color-0) !important;
  }
  
  .plyr--full-ui a, .plyr--full-ui button, .plyr--full-ui input, .plyr--full-ui label {
      border: 0px !important;
  }
  
  .plyr--video .plyr__control.plyr__tab-focus, .plyr--video .plyr__control:hover, .plyr--video .plyr__control[aria-expanded=true] {
      background: var(--ast-global-color-0) !important;
      border-color: var(--ast-global-color-0) !important;
      color: #fff !important;
  }
  
  
  
  .plyr--video .plyr__control.plyr__tab-focus, .plyr--video .plyr__control:acitve, .plyr--video .plyr__control[aria-expanded=true] {
      background: var(--ast-global-color-0);
      border-color: var(--ast-global-color-0);
  }
  
  [type=button]:focus, [type=button]:hover, [type=submit]:focus, [type=submit]:hover, button:focus, button:hover {
      background-color: var(--ast-global-color-0);
      border-color: var(--ast-global-color-0);
  
  }
  
  .plyr__menu__container .plyr__control>span {
      color: #000 !important;
  }
  
  
  /*----------------------------*/
  
  /* Tablet */
  .tutor-tab {
          background-color: #121212 !important;
  }
  
  .tutor-video-player-wrapper{
              background-color: #121212 !important;
  }
  
      /* Rodapé Progresso - Celular */
  .tutor-spotlight-mobile-progress-complete {
      background: #424242;
      box-shadow: 0px 0px 16px 0 rgba(176,182,209,0.180862);
          padding-right: 20px;
      padding-left: 20px;
      margin-top: 5px;
  }
      /* Botoão Marcar como Concluido - Celular */
  
  .tutor-ws-nowrap {
      font-size: 11px;
      font-weight: 900;
      white-space: normal;
  }
  
  
  /*----------------------------*/
  
  /* Painel - Perfil */
  .tutor-dashboard .tutor-frontend-dashboard-header{
          background-color: #16161e;
      border-radius: 20px;
      padding: 4%;
      padding-top: 4%;
      margin-bottom: 2%;
  }
  
  .tutor-dashboard-left-menu{
      display: none;
  
  }
  
  .tutor-dashboard-content, .tutor-col-lg-9{
      width: 100% !important;
  }
  
  
  .tutor-dashboard .tutor-frontend-dashboard-header:after {
      display: none;
  }
  
  .tutor-dashboard-left-menu{
      padding: 4%;
      margin-bottom: 2%;
  }
  
  .tutor-dashboard .tutor-frontend-dashboard-maincontent ul.tutor-dashboard-permalinks:before{
      display: none;
  }
  
  .tutor-dashboard-menu-divider{
      display: none;
  }
  
  .tutor-dashboard .tutor-dashboard-left-menu .tutor-dashboard-permalinks li.active a {
      background: #24242E;
      border-radius: 10px;
  
  }
  
  .tutor-dashboard .tutor-dashboard-left-menu .tutor-dashboard-permalinks li a:hover {
      background-color: #24242E !important;
      border-radius: 10px;
  }
  
  
  .tutor-dashboard-menu-item-link {
          vertical-align: baseline !important;
  }
  
  .tutor-dashboard-menu-item-text{
      color: #fff;
      line-height: 20px;
  }
  
  .tutor-fs-5.tutor-fw-medium.tutor-mb-24 {
      color: #fff;
      background-color: #16161e;
      border-radius: 10px;
      padding: 2%;
      padding-left: 5%;
      margin-bottom: 2%;
      font-weight: 900;
  }
  
  .tutor-dashboard .tutor-dashboard-content #tutor_profile_cover_photo_editor #tutor_photo_meta_area>span>span{
      color: #7a7a7a;
  }
  
  input.tutor-form-control{
      background: #16161E;
      border-radius: 10px;
      color: #fff;
      border-color: #434C58 !important;
  }
  
  input.tutor-form-control:disabled{
      background: #16161E;
      color: #fff;
  }
  
  div.mce-panel {
      background: #16161E !important;
      color: #fff !important;
  }
  
  .tutor-dashboard .tutor-dashboard-content .tutor-dashboard-setting-social .tutor-social-field>div:first-child {
      color: #fff;
  }
  
  .tutor-dashboard .tutor-dashboard-content .tutor-dashboard-setting-social .tutor-social-field>div:first-child i {
      color: #fff;
  }
  
  label.field-label.tutor-form-label{
      color: #fff;
  }
  
  .tutor-dashboard .tutor-dashboard-content #tutor_profile_cover_photo_editor #tutor_photo_meta_area > span {
      white-space: normal;
  }
  
  .tutor-dashboard .tutor-dashboard-content #tutor_profile_cover_photo_editor #tutor_cover_area .tutor_cover_uploader {
      font-size: 12px;
      margin-left: 50%;
      right: 15px;
  }
  
  .tutor-wrap-parent {
      margin-left: 2% !important;
      margin-right: 2% !important;
      padding-top: 20px !important;
  }
  div.tutor-dashboard-content-inner.tutor-dashboard-profile-data
      {padding-left: 20px;
      padding-top: 4px;
   line-height: 8px;
  }
  
  
  /*----------------------------*/
  
  /* Mensagem bloqueio */
  .tutor-card{
      background-color: #121212 !important;
  }
  
  /* Celular */
  
  
  /* Inicia Media - 390 */
  @media only screen and (max-width: 390px) {
      /* Botoão Marcar como Concluido - Celular */
      .tutor-ws-nowrap {
      font-size: 12px;
      font-weight: 900;
      white-space: normal;
  }
      
  .tutor-spotlight-mobile-progress-complete {
      margin-top: 50px !important;
  }
      .tutor-mr-20 {
          margin-right: 0px;	}
          .tutor-wrap-parent {
      margin-left: 0px !important;
  }
          .tutor-dashboard #tutor-dashboard-footer-mobile{
          padding-top: 10px !important;
          padding-bottom: 5px !important;
              
      }
  }
  
  /* Finaliza Media - 391 */
  
  
  /* Inicia Media - 544 */
  @media (max-width: 544px){
  .ast-separate-container #content .ast-container {
      padding-left: 20px;
  }
  div.tutor-dashboard-content-inner.tutor-dashboard-profile-data
      {padding-left: 21px;
      padding-top: 10px;}
      
      .tutor-col-12.tutor-col-md-8.tutor-col-lg-9 {
      margin-right: 20px !important;
  }
  
      
}
  /* Finaliza Media - 544 */
  
  
  /* Inicia Media - 768 */
  @media (max-width: 768px){
  .tutor-header-right-side {
      margin-top: 0px;
      margin-bottom: 0px;
  }
          .tutor-dashboard .tutor-frontend-dashboard-header{
      margin-right: 1%;
              padding-top: 7%;
  }
      
      .tutor-fs-5.tutor-fw-medium.tutor-mb-24{
          margin-right: 1%;
          margin-right: 3% !important
      }
      
      .tutor-dashboard #tutor-dashboard-footer-mobile{
                  background-color: #16161e;
      border-radius: 5px;
  margin-left: 10px;
          margin-right: 10px;
          box-shadow: none;
          padding-top: 10px;
          padding-bottom: 10px;
              
      }
      
      .tutor-dashboard #tutor-dashboard-footer-mobile>div>div>a span {
      display: block;
      font-size: 11px;
      text-align: center !important;
      line-height: 13px;
  }
      
      .tutor-wrap-parent {
          margin-left: 0px !important;
          padding-top: 20px !important;
          padding-left: 10px !important;
          padding-right: 10px !important;
      }
      
          .tutor-course-single-sidebar-title {
          display: flex !important;
          justify-content: flex-end !important; /* Alinha o conteúdo à direita */
          align-items: center !important; /* Centraliza verticalmente */
                  padding: 0px !important;
      }
  
      .tutor-course-single-sidebar-title .tutor-fs-6.tutor-fw-medium.tutor-color-secondary {
          display: none;
      }
  
      .tutor-course-single-sidebar-title .tutor-d-block.tutor-d-xl-none {
          display: block;
      }
      
      .tutor-course-topic-single-footer {
          display: flex !important;
      }/* Botão Próximo e Anterior */
      
 .tutor-dashboard-content {
    margin-bottom: 0px !important;
 }

     /* Estilos específicos para PWA em modo standalone ou fullscreen no mobile */
     .pwa-standalone .pwa-menu-mobile {
        padding-bottom: 8px;
        
            }
      
  }
  /* Finaliza Media - 768 */
  
  
  /* Inicia Media - 800 */
  @media (min-width: 800px){
  .tutor-course-details-page .tutor-course-details-tab .tutor-is-sticky {
      background: #424242;
      border-radius: 10px;
  }
      
      .tutor-col-md-4 {
      flex: 0 0 auto;
      width: 35%;
  }
      .tutor-col-md-8 {
      flex: 0 0 auto;
      width: 65%;
  }
      
      .tutor-header-right-side{
          margin-bottom: 0px !important;
      }
      
      
  
      
  }
  
  /* Finaliza Media - 800 */
  
  /* Inicia Media - 921 */
  
  @media only screen and (max-width: 921px) {
      .tutor-video-player-wrapper {
          margin: 10px;
      }	
      
  .tutor-dashboard .tutor-dashboard-content #tutor_profile_cover_photo_editor #tutor_profile_area {
      left: 15px;
      
  }
      .tutor-dashboard .tutor-dashboard-content #tutor_profile_cover_photo_editor #tutor_cover_area .tutor_cover_uploader {
      font-size: 10px;
      margin-left: 50%;
      right: 8px;
          line-height: 12px;
          
  }
      i.tutor-icon-camera.tutor-mr-12{
          margin-right: 5px;
      }
      
              .tutor-dashboard .tutor-frontend-dashboard-header{
      margin-right: 2%;
  }
      
      .tutor-fs-5.tutor-fw-medium.tutor-mb-24 {
          margin-right: 3%;
      }
      
  /* Espaçamento entre o cabeçalho e o conteúdo */
  .tutor-course-single-content-wrapper {
    padding: 10px !important;
  }
  .tutor-course-single-sidebar-open{
      margin: 10px !important;
  }
     
      
  }
  /* Finaliza Media - 921 */
  
  
  /* Inicia Media - 992 */
  @media (min-width: 992px){
  .tutor-col-lg-3 {
      flex: 0 0 auto;
      width: 40%;
  }
      .tutor-col-lg-9 {
      flex: 0 0 auto;
      width:60%;
  }
      
  }
  /* Finaliza Media - 992 */
  
  /* Inicia Media - 1200 */
  @media (max-width: 1200px){
      
      .tutor-spotlight-mobile-progress-complete {
          background: #424242;}
      
      .tutor-row>*{
          padding-right: 10px;
          padding-left: 10px;
      }
      
      .tutor-px-20 {
          padding-left: none !important;
      }
      
      .tutor-spotlight-mobile-progress-left, .tutor-spotlight-mobile-progress-right{
          width: 100% !important;
      }
      
  .tutor-topbar-complete-btn button{
          width: 100% !important;
      justify-content: center;
      vertical-align: middle !important;
      }
      
      .tutor-topbar-complete-btn.tutor-mr-20{
  margin-right: 0px !important;
      }
      
  .tutor-course-single-content-wrapper.tutor-course-single-sidebar-open .tutor-course-single-sidebar-wrapper {
  background-color: #141414 !important;
  }
              .tutor-video-player-wrapper {
      margin: 8px;
  }
      .tutor-course-single-sidebar-wrapper
      {
          margin: 5px !important;
          width: 100% !important;
      }
  
  
  }
  
  /* Finaliza Media - 1200 */

    /* Finaliza Media - 1024 */
    @media(max-width: 1024px) {
        .tutor-spotlight-mobile-progress-complete {
            background-color: #121212!important;
          border: 1px solid rgba(255, 255, 255, 0.05) !important;
          box-shadow: none !important;
          border-radius: 10px !important;
            margin-left: 10px !important;
            margin-right: 10px !important;
            margin-top: 23px!important;
        }
        
                  .tutor-course-single-sidebar-title {
              display: flex !important;
              justify-content: flex-end !important; /* Alinha o conteúdo à direita */
              align-items: center !important; /* Centraliza verticalmente */
                      padding: 0px !important;
          }
      
          .tutor-course-single-sidebar-title .tutor-fs-6.tutor-fw-medium.tutor-color-secondary {
              display: none;
          }
      
          .tutor-course-single-sidebar-title .tutor-d-block.tutor-d-xl-none {
              display: block;
          }
    }
  
  /* Media - Ajuste Sobre Aula - 1200 */
  
  @media (min-width: 1200px) {
      .tutor-col-xl-8 {
          flex: 0 0 auto;
          width: 100%;
      }
  }
  
  @media (min-width: 1400px) {
      .tutor-container-xxl, .tutor-container-xl, .tutor-container-lg, .tutor-container-md, .tutor-container-sm, .tutor-container {
          max-width: 98% !important;
      }
  }
  
  /* Finaliza - Media - Ajuste Sobre Aula - 1200 */

  @media(max-width: 1199.98px) {
    .tutor-course-single-content-wrapper.tutor-course-single-sidebar-open .tutor-course-single-sidebar-wrapper {
        z-index: 9999 !important;
    }
  }