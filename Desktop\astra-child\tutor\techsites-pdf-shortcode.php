<?php
if (!defined('ABSPATH')) {
    exit('Acesso direto não permitido');
}

function techsites_pdf($atts) {
    $atts = shortcode_atts(array(
        'id' => '',
    ), $atts, 'techsites_pdf');
    
    $pdf_id = intval($atts['id']);
    
    if (!$pdf_id) {
        return 'Por favor, forneça um ID de mídia válido.';
    }
    
    // Verifica se o anexo existe
    $attachment = get_post($pdf_id);
    if (!$attachment) {
        return 'PDF não encontrado.';
    }
    
    $pdf_url = get_stylesheet_directory_uri() . '/tutor/serve-pdf.php?id=' . $pdf_id . '&t=' . time();
    $pdf_js_viewer = get_stylesheet_directory_uri() . '/pdfjs/web/viewer.html';
    
    return '<iframe src="' . esc_url($pdf_js_viewer) . '?file=' . urlencode($pdf_url) . '" width="100%" height="600" style="border: none;"></iframe>';
}
add_shortcode('techsites_pdf', 'techsites_pdf');