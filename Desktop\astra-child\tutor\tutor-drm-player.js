document.addEventListener('DOMContentLoaded', function () {
    var userInfo = window.tutorUserInfo || {};
    var overlaySettings = window.tutorOverlaySettings || {};

    // Verifica se o overlay está habilitado
    if (overlaySettings.enabled == 1) {
        displayUserInfo();
    } else {
        console.log('Overlay DRM não está ativado para este curso.');
        return;
    }

    function displayUserInfo() {
        var videoContainer = document.querySelector('.tutor-video-player');

        if (videoContainer) {
            var infoElement = document.createElement('div');
            infoElement.id = 'tutor-user-info';
            infoElement.style.color = 'white';

            // Verifica e aplica a cor de fundo com transparência
            var bgColor = hexToRGBA(overlaySettings.background_color, overlaySettings.background_opacity);
            infoElement.style.backgroundColor = bgColor;

            infoElement.style.padding = '10px';
            infoElement.style.position = 'absolute';
            infoElement.style.zIndex = 9999;
            infoElement.style.pointerEvents = 'none';
            infoElement.style.fontSize = overlaySettings.font_size + 'px';

            // Função para verificar se um campo está preenchido
            function isValidField(field) {
                return field && field.trim() !== '' && field !== 'false' && field !== 'undefined';
            }

            // Função para formatar as informações do usuário
            function formatUserInfo() {
                var parts = [];
                var hasValidData = false;
                
                switch (overlaySettings.display_option) {
                    case 'name_email':
                        if (isValidField(userInfo.name)) parts.push("Nome: " + userInfo.name);
                        if (isValidField(userInfo.email)) parts.push("Email: " + userInfo.email);
                        hasValidData = parts.length > 0;
                        break;

                    case 'name_email_cpf':
                        if (isValidField(userInfo.name)) parts.push("Nome: " + userInfo.name);
                        if (isValidField(userInfo.email)) parts.push("Email: " + userInfo.email);
                        if (isValidField(userInfo.cpf_cnpj)) parts.push("CPF: " + userInfo.cpf_cnpj);
                        hasValidData = isValidField(userInfo.name) || isValidField(userInfo.email);
                        break;

                    case 'name_email_cpf_whatsapp':
                        if (isValidField(userInfo.name)) parts.push("Nome: " + userInfo.name);
                        if (isValidField(userInfo.email)) parts.push("Email: " + userInfo.email);
                        if (isValidField(userInfo.cpf_cnpj)) parts.push("CPF: " + userInfo.cpf_cnpj);
                        if (isValidField(userInfo.whatsapp)) parts.push("WhatsApp: " + userInfo.whatsapp);
                        hasValidData = isValidField(userInfo.name) || isValidField(userInfo.email);
                        break;

                    case 'name_cpf':
                        if (isValidField(userInfo.name)) parts.push("Nome: " + userInfo.name);
                        if (isValidField(userInfo.cpf_cnpj)) parts.push("CPF: " + userInfo.cpf_cnpj);
                        hasValidData = isValidField(userInfo.name);
                        break;

                    case 'name_whatsapp':
                        if (isValidField(userInfo.name)) parts.push("Nome: " + userInfo.name);
                        if (isValidField(userInfo.whatsapp)) parts.push("WhatsApp: " + userInfo.whatsapp);
                        hasValidData = isValidField(userInfo.name);
                        break;

                    case 'name_only':
                        if (isValidField(userInfo.name)) {
                            parts.push("Nome: " + userInfo.name);
                            hasValidData = true;
                        }
                        break;

                    case 'cpf_only':
                        if (isValidField(userInfo.cpf_cnpj)) {
                            parts.push("CPF: " + userInfo.cpf_cnpj);
                            hasValidData = true;
                        }
                        break;

                    case 'email_only':
                        if (isValidField(userInfo.email)) {
                            parts.push("Email: " + userInfo.email);
                            hasValidData = true;
                        }
                        break;

                    case 'whatsapp_only':
                        if (isValidField(userInfo.whatsapp)) {
                            parts.push("WhatsApp: " + userInfo.whatsapp);
                            hasValidData = true;
                        }
                        break;

                    default:
                        if (isValidField(userInfo.name)) parts.push("Nome: " + userInfo.name);
                        if (isValidField(userInfo.email)) parts.push("Email: " + userInfo.email);
                        hasValidData = parts.length > 0;
                }
                
                // Retorna null se não houver dados válidos para exibir
                return hasValidData ? parts.join(" | ") : null;
            }

            // Formatação e exibição das informações
            var displayText = formatUserInfo();
            if (displayText) {
                infoElement.innerHTML = displayText;
                videoContainer.style.position = 'relative';
                videoContainer.appendChild(infoElement);

                function moveUserInfo() {
                    var positions = [
                        { top: '10px', left: '10px' },
                        { top: '10px', right: '10px' },
                        { bottom: '10px', left: '10px' },
                        { bottom: '10px', right: '10px' }
                    ];
                    var randomPosition = positions[Math.floor(Math.random() * positions.length)];

                    infoElement.style.top = '';
                    infoElement.style.left = '';
                    infoElement.style.right = '';
                    infoElement.style.bottom = '';

                    Object.assign(infoElement.style, randomPosition);
                }

                moveUserInfo();
                setInterval(moveUserInfo, 5000);
            } else {
                console.log('Nenhuma informação válida para exibir no overlay.');
            }
        } else {
            console.log('Contêiner de vídeo não encontrado.');
        }
    }

    // Função para converter cor hexadecimal para RGBA
// Função para converter cor hexadecimal para RGBA
function hexToRGBA(hex, opacity) {
    opacity = parseFloat(opacity);
    if (isNaN(opacity)) {
        opacity = 1;
    }
    // Garante que a opacidade esteja entre 0 e 1
    opacity = Math.max(0, Math.min(1, opacity));
    
    // Remove o # se presente
    hex = hex.replace('#', '');
    
    // Converte hex para RGB
    var r = parseInt(hex.substring(0, 2), 16);
    var g = parseInt(hex.substring(2, 4), 16);
    var b = parseInt(hex.substring(4, 6), 16);
    
    // Se a opacidade for 0, define uma opacidade mínima para manter o elemento clicável
    var finalOpacity = opacity === 0 ? 0.01 : opacity;
    
    return 'rgba(' + r + ', ' + g + ', ' + b + ', ' + finalOpacity + ')';
}
});