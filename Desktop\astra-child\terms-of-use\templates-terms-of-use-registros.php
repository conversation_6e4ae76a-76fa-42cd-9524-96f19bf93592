<?php
/**
 * Template para a página de registros do Termo de Uso
 */
if (!defined('ABSPATH')) exit;
?>
<div class="wrap termo-uso-admin">
    <h1 class="termo-titulo">Registros de Aceite do Termo</h1>
    
    <div class="termo-card">
        <div class="termo-table-container">
            <table class="termo-table">
                <thead>
                    <tr>
                        <th>Usuário</th>
                        <th>CPF/CNPJ</th>
                        <th>WhatsApp</th>
                        <th>E-mail</th>
                        <th>IP</th>
                        <th>Data de Aceite</th>
                        <th>Navegador</th>
                        <th>Versão do Termo</th>
                        <th>Baixar</th>
                    </tr>
                </thead>
                <tbody>
                    <?php if (!empty($registros)): ?>
                        <?php foreach ($registros as $registro): 
                            $user_info = $this->get_user_info($registro->user_id);
                            if (!$user_info) continue;
                        ?>
                            <tr>
                                <td><?php echo esc_html($user_info['name']); ?></td>
                                <td>
                                    <?php if ($user_info['cpf']): ?>
                                        <?php echo esc_html($user_info['cpf']); ?>
                                    <?php else: ?>
                                        <span class="badge badge-warning">Não informado</span>
                                    <?php endif; ?>
                                </td>
                                <td>
                                    <?php if ($user_info['whatsapp']): ?>
                                        <?php echo esc_html($user_info['whatsapp']); ?>
                                    <?php else: ?>
                                        <span class="badge badge-warning">Não informado</span>
                                    <?php endif; ?>
                                </td>
                                <td><?php echo esc_html($user_info['email']); ?></td>
                                <td>
                                    <?php echo $registro->ip ? esc_html($registro->ip) : '<span class="badge badge-warning">Não informado</span>'; ?>
                                </td>
                                <td><?php echo esc_html(date('d/m/Y H:i:s', strtotime($registro->data_aceite))); ?></td>
                                <td class="termo-browser-col">
                                    <?php if (!empty($registro->navegador)): ?>
                                        <span class="termo-tooltip" title="<?php echo esc_attr($registro->navegador); ?>">
                                            <?php echo esc_html(substr($registro->navegador, 0, 30)) . '...'; ?>
                                        </span>
                                    <?php else: ?>
                                        <span class="badge badge-warning">Não informado</span>
                                    <?php endif; ?>
                                </td>
                                <td><?php echo esc_html($registro->versao_termo); ?></td>
                                <td>
                                    <a href="<?php echo admin_url('admin-ajax.php?action=baixar_termo&registro_id=' . $registro->id); ?>" class="button">
                                        Baixar Termo
                                    </a>
                                </td>
                            </tr>
                        <?php endforeach; ?>
                    <?php else: ?>
                        <tr>
                            <td colspan="9" class="termo-empty-message">Nenhum registro encontrado.</td>
                        </tr>
                    <?php endif; ?>
                </tbody>
            </table>
        </div>
        
        <?php if ($total_pages > 1): ?>
            <div class="termo-paginacao">
                <?php
                echo paginate_links(array(
                    'base' => add_query_arg('paged', '%#%'),
                    'format' => '',
                    'prev_text' => 'Anterior',
                    'next_text' => 'Próximo',
                    'total' => $total_pages,
                    'current' => $page,
                    'type' => 'list'
                ));
                ?>
            </div>
        <?php endif; ?>
    </div>
</div>
