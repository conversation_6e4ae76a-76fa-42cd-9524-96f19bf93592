(function($) {
    var clickLock = false;

    $(document).ready(function() {
        // Alternância da barra lateral
        $('.sidebar-toggle').on('click', function() {
            handleSidebarToggle();
        });

        // Manipulação de navegação
        $('.navigation .elementor-icon-list-item').on('click', function() {
            handleNavigationClick($(this));
        });
    });

    $(window).on('load resize', function() {
        handleWindowResize();
        toggleMenuAlignment();
    });

    $(window).on('load scroll', function() {
        updateActiveNavigationOnScroll();
    });

    $('body').on('click', function(e) {
        handleBodyClick(e);
    });

    // Função para manipular a alternância da barra lateral
    function handleSidebarToggle() {
        var $body = $('body');
        var windowWidth = $(window).width();

        if (windowWidth < 768) {
            $body.toggleClass('left');
            toggleMenuAlignment();
        } else {
            if ($body.hasClass('open')) {
                $body.removeClass('open');
                setTimeout(function() {
                    $body.removeClass('opening');
                    toggleMenuAlignment();
                }, 300);
            } else {
                $body.addClass('opening');
                setTimeout(function() {
                    $body.addClass('open');
                    toggleMenuAlignment();
                }, 300);
            }
        }
    }

    // Função para manipular o clique na navegação
    function handleNavigationClick($element) {
        $('.navigation .elementor-icon-list-item').removeClass('active');
        $element.addClass('active');
        clickLock = true;
        setTimeout(function() {
            clickLock = false;
        }, 500);
        $('body').toggleClass('left');
        toggleMenuAlignment();
    }

    // Função para ajustar a barra lateral com base no redimensionamento da janela
    function handleWindowResize() {
        var windowWidth = $(window).width();
        var $body = $('body');

        if (windowWidth < 768) {
            $body.removeClass('open opening').addClass('left');
        } else if (windowWidth < 1025) {
            $body.addClass('open opening');
        } else {
            $body.removeClass('open opening');
        }
    }

    // Função para atualizar o item de navegação ativo ao rolar a página
    function updateActiveNavigationOnScroll() {
        if (!hash || clickLock) return;

        var ids = [];
        $('.elementor-top-section').each(function() {
            if ($(window).scrollTop() + $(window).height() - 350 > $(this).offset().top) {
                ids.push($(this).attr('id'));
            }
        });

        // Remove IDs indefinidos
        ids = ids.filter(function(element) {
            return element !== undefined;
        });

        var id = ids[ids.length - 1];
        $('.elementor-icon-list-item').removeClass('active');
        if (id) {
            $('[href="#' + id + '"]').parent().addClass('active');
        }
    }

    // Função para manipular cliques fora da área do menu
    function handleBodyClick(e) {
        var $target = $(e.target);
        var windowWidth = $(window).width();

        if (!$target.closest('.elementor-location-header').length && !$target.hasClass('elementor-location-header')) {
            if (windowWidth < 768) {
                $('body').addClass('left');
            } else if (windowWidth < 1025) {
                $('body').addClass('open opening');
            }
        }
        toggleMenuAlignment();
    }

    // Função para alternar o alinhamento do menu com base no estado do menu
    function toggleMenuAlignment() {
        if ($('body').hasClass('open')) {
            $('.elementor-icon-list-items .elementor-icon-list-item').removeClass('navigation-left').addClass('navigation-center');
        } else {
            $('.elementor-icon-list-item').removeClass('navigation-center').addClass('navigation-left');
        }
    }

    // Inicializar o estado do menu e navegação quando a página for carregada
    $(window).on('load', function() {
        var currentUrl = location.protocol + '//' + location.host + location.pathname;

        $('.elementor-icon-list-item a').each(function() {
            if ($(this).attr('href') === currentUrl) {
                hash = false;
                $(this).parent().addClass('active');
            }
        });

        // Abrir o menu se a largura for maior que 768px, mantendo o comportamento original
        if ($(window).width() >= 768) {
            $('body').addClass('open opening');
        }

        toggleMenuAlignment();
    });

    var hash = true;

})(jQuery);
