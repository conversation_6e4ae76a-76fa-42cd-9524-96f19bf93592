jQuery(document).ready(function($) {
    let termoLido = false;
    const $termoConteudo = $('.termo-uso-conteudo');
    const $aceitarBtn = $('#aceitar-termo');
    const $overlay = $('.termo-uso-overlay');
    const $loading = $('.termo-uso-loading');
    const $success = $('.termo-uso-success');
    const $backdrop = $('.termo-backdrop');

    // Esconder mensagens inicialmente
    $loading.hide();
    $success.hide();
    $backdrop.hide();

    // Aplicar cores dinâmicas
    function aplicarCores() {
        const options = termoUsoAjax.options || {};
        
        // Cores principais
        document.documentElement.style.setProperty('--cor-texto', options.cor_texto || '#000000');
        document.documentElement.style.setProperty('--cor-fundo', options.cor_fundo || '#ffffff');
        document.documentElement.style.setProperty('--cor-botao-aceitar', options.cor_botao_aceitar || '#4CAF50');
        document.documentElement.style.setProperty('--cor-botao-sair', options.cor_botao_sair || '#f44336');

        // Aplicar cores diretamente nos elementos também (fallback)
        $('.termo-uso-container').css('background-color', options.cor_fundo || '#ffffff');
        $('.termo-uso-container').css('color', options.cor_texto || '#000000');
        $('.termo-btn-primary').css('background-color', options.cor_botao_aceitar || '#4CAF50');
        $('.termo-btn-danger').css('background-color', options.cor_botao_sair || '#f44336');
    }
    
    aplicarCores();
        
    // Monitorar o scroll do termo
    $termoConteudo.on('scroll', function() {
        const scrollPosition = $(this).scrollTop() + $(this).innerHeight();
        const scrollHeight = $(this)[0].scrollHeight - 30;

        if (scrollPosition >= scrollHeight && !termoLido) {
            termoLido = true;
            $aceitarBtn.removeClass('disabled');
            $aceitarBtn.css('opacity', '1');
        }
    });

    // Aceitar termo
    $aceitarBtn.on('click', function(e) {
        e.preventDefault();

        if (!termoLido) {
            Swal.fire({
                icon: 'warning',
                title: 'Leia o termo!',
                text: 'Por favor, leia o termo até o final antes de aceitar.',
            });
            return;
        }

        $.ajax({
            url: termoUsoAjax.ajaxurl,
            type: 'POST',
            data: {
                action: 'registrar_aceite'
            },
            beforeSend: function() {
                $aceitarBtn.prop('disabled', true);
                $backdrop.fadeIn();
                $loading.fadeIn();
            },
            success: function(response) {
                if (response.success) {
                    $loading.fadeOut(function() {
                        Swal.fire({
                            icon: 'success',
                            title: 'Aceite registrado!',
                            text: 'O termo foi aceito com sucesso.',
                            timer: 2000,
                            showConfirmButton: false,
                        }).then(() => {
                            window.location.reload();
                        });
                    });
                } else {
                    handleError(response.data || 'Erro ao registrar aceite. Por favor, tente novamente.');
                }
            },
            error: function() {
                handleError('Erro ao registrar aceite. Por favor, tente novamente.');
            }
        });
    });

    // Função para tratar erros
    function handleError(message) {
        Swal.fire({
            icon: 'error',
            title: 'Erro',
            text: message,
        });
        $aceitarBtn.prop('disabled', false);
        $loading.fadeOut();
        $success.hide();
        $backdrop.fadeOut();
    }

    // Botão Sair
    $('#sair-termo').on('click', function(e) {
        e.preventDefault();
        Swal.fire({
            icon: 'question',
            title: 'Deseja sair?',
            text: 'Você será desconectado do sistema.',
            showCancelButton: true,
            confirmButtonText: 'Sim, sair',
            cancelButtonText: 'Cancelar',
        }).then((result) => {
            if (result.isConfirmed) {
                window.location.href = termoUsoAjax.logout_url;
            }
        });
    });
});
