<?php
/**
 * Template para a página de configurações do Termo de Uso
 */
if (!defined('ABSPATH')) exit;
?>
<div class="wrap termo-uso-admin">
    <h1 class="termo-titulo">Configurações do Termo de Uso</h1>
    
    <div class="termo-card">
        <form method="post" action="">
            <!-- Ativação -->
            <div class="termo-section termo-section-toggle">
                <label class="termo-toggle-label">
                    <span class="termo-label-text">Ativar Termo de Uso</span>
                    <div class="termo-toggle">
                        <input type="checkbox" name="ativo" value="1" <?php checked($options['ativo'] ?? '', '1'); ?>>
                        <span class="termo-toggle-slider"></span>
                    </div>
                </label>
            </div>

            <!-- Editor -->
            <div class="termo-section">
                <div class="termo-section-header">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"/>
                        <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"/>
                    </svg>
                    <h3 class="termo-section-title">Editor do Termo</h3>
                </div>
            
                <?php 
                // Campo do editor de texto
                wp_editor($options['texto_termo'] ?? '', 'texto_termo', array(
                    'textarea_rows' => 15,
                    'editor_height' => 300,
                    'media_buttons' => true,
                    'teeny' => false,
                    'quicktags' => true
                )); 
                ?>
            <div class="termo-input-versao-termo">
                <label for="versao_termo">Versão do Termo</label>
                <input 
                    type="text" 
                    pattern="[0-9]+(\.[0-9]+)?" 
                    id="versao_termo" 
                    name="versao_termo" 
                    value="<?php echo esc_attr($options['versao_termo'] ?? '1.0'); ?>"
                    placeholder="Ex: 1.0 ou 1.1" 
                />
                <p class="description">A versão do termo deve ser maior que a anterior. Uma vez salva, não é possível retroceder.</p>
            </div>
            </div>


                <!-- Shortcodes -->
            <div class="termo-shortcodes-info">
                <div class="termo-shortcodes-header">
                    <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M16 18l6-6-6-6M8 6l-6 6 6 6"/>
                    </svg>
                    <h4>Shortcodes Disponíveis</h4>
                </div>
                <div class="termo-shortcodes-grid">
                    <!-- Dados do Site -->
                    <div class="termo-shortcode-group">
                        <h5>Dados do Site</h5>
                        <div class="termo-shortcode-card">
                            <code>[titulo_site]</code>
                            <span>Título do site</span>
                        </div>
                        <div class="termo-shortcode-card">
                            <code>[url_site]</code>
                            <span>Endereço do site</span>
                        </div>
                        <div class="termo-shortcode-card">
                            <code>[data_hora]</code>
                            <span>Data e hora atual</span>
                        </div>
                    </div>
            
                    <!-- Dados do Usuário -->
                    <div class="termo-shortcode-group">
                        <h5>Dados do Usuário</h5>
                        <div class="termo-shortcode-card">
                            <code>[nome_usuario]</code>
                            <span>Nome do usuário</span>
                        </div>
                        <div class="termo-shortcode-card">
                            <code>[whatsapp_usuario]</code>
                            <span>WhatsApp</span>
                        </div>
                        <div class="termo-shortcode-card">
                            <code>[cpf_usuario]</code>
                            <span>CPF/CNPJ</span>
                        </div>
                        <div class="termo-shortcode-card">
                            <code>[email_usuario]</code>
                            <span>E-mail</span>
                        </div>
                    </div>
                    
                    <!-- Dados de Localização -->
                    <div class="termo-shortcode-group">
                        <h5>Dados de Localização</h5>
                        <div class="termo-shortcode-card">
                            <code>[cidade_usuario]</code>
                            <span>Cidade</span>
                        </div>
                        <div class="termo-shortcode-card">
                            <code>[estado_usuario]</code>
                            <span>Estado</span>
                        </div>
                        <div class="termo-shortcode-card">
                            <code>[pais_usuario]</code>
                            <span>País</span>
                        </div>
                    </div>
                    
                    <!-- Dados do Sistema -->
                    <div class="termo-shortcode-group">
                        <h5>Dados do Sistema</h5>
                        <div class="termo-shortcode-card">
                            <code>[ip_usuario]</code>
                            <span>IP</span>
                        </div>
                        <div class="termo-shortcode-card">
                            <code>[navegador_usuario]</code>
                            <span>Navegador</span>
                        </div>
                        <div class="termo-shortcode-card">
                            <code>[dispositivo_usuario]</code>
                            <span>Dispositivo</span>
                        </div>
                        <div class="termo-shortcode-card">
                            <code>[sistema_usuario]</code>
                            <span>Sistema</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- IPInfo Token -->
            <div class="termo-section">
                <div class="termo-section-header">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"/>
                    </svg>
                    <h3 class="termo-section-title">Geolocalização</h3>
                </div>
                
                <div class="termo-input-card">
                    <div class="termo-input-header">
                        <span class="termo-label-text">Token IPInfo.io</span>
                        <a href="https://ipinfo.io/signup" target="_blank" class="termo-help-link">
                            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                <circle cx="12" cy="12" r="10"/>
                                <path d="M12 16v-4M12 8h.01"/>
                            </svg>
                            Obter token
                        </a>
                    </div>
                    <input type="text" 
                           name="ipinfo_token" 
                           value="<?php echo esc_attr($options['ipinfo_token'] ?? ''); ?>" 
                           class="termo-input"
                           placeholder="Insira seu token do IPInfo.io">
                    <p class="termo-input-help">Token necessário para obter dados de localização dos usuários.</p>
                </div>
            </div>

            <!-- Personalização Visual -->
            <div class="termo-section">
                <div class="termo-section-header">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M12 19l7-7 3 3-7 7-3-3z"/>
                        <path d="M18 13l-1.5-7.5L2 2l3.5 14.5L13 18l5-5z"/>
                        <path d="M2 2l7.586 7.586"/>
                        <circle cx="11" cy="11" r="2"/>
                    </svg>
                    <h3 class="termo-section-title">Personalização Visual</h3>
                </div>

                <!-- Cores -->
                <div class="termo-subsection">
                    <div class="termo-subsection-header">
                        <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <circle cx="12" cy="12" r="10"/>
                            <path d="M12 2v20M2 12h20"/>
                        </svg>
                        <h4 class="termo-subsection-title">Cores</h4>
                    </div>
                    
                    <div class="termo-colors-grid">
                        <!-- Cores do Tema -->
                        <div class="termo-color-card">
                            <label>
                                <span class="termo-label-text">Cor do Texto</span>
                                <div class="termo-color-input">
                                    <input type="color" name="cor_texto" 
                                           value="<?php echo esc_attr($options['cor_texto'] ?? '#000000'); ?>">
                                </div>
                            </label>
                        </div>
                        <div class="termo-color-card">
                            <label>
                                <span class="termo-label-text">Cor do Fundo</span>
                                <div class="termo-color-input">
                                    <input type="color" name="cor_fundo" 
                                           value="<?php echo esc_attr($options['cor_fundo'] ?? '#ffffff'); ?>">
                                </div>
                            </label>
                        </div>
                        <!-- Cores dos Botões -->
                        <div class="termo-color-card">
                            <label>
                                <span class="termo-label-text">Cor do Botão Aceitar</span>
                                <div class="termo-color-input">
                                    <input type="color" name="cor_botao_aceitar" 
                                           value="<?php echo esc_attr($options['cor_botao_aceitar'] ?? '#4CAF50'); ?>">
                                </div>
                            </label>
                        </div>
                        <div class="termo-color-card">
                            <label>
                                <span class="termo-label-text">Cor do Botão Sair</span>
                                <div class="termo-color-input">
                                    <input type="color" name="cor_botao_sair" 
                                           value="<?php echo esc_attr($options['cor_botao_sair'] ?? '#f44336'); ?>">
                                </div>
                            </label>
                        </div>
                    </div>
                </div>

                <!-- Textos dos Botões -->
                <div class="termo-subsection">
                    <div class="termo-subsection-header">
                        <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <rect x="3" y="3" width="18" height="18" rx="2"/>
                            <path d="M7 7h10M7 12h10M7 17h10"/>
                        </svg>
                        <h4 class="termo-subsection-title">Textos dos Botões</h4>
                    </div>
                    <div class="termo-buttons-grid">
                        <div class="termo-input-card">
                            <label>
                                <span class="termo-label-text">Botão Aceitar</span>
                                <input type="text" 
                                       name="texto_botao_aceitar" 
                                       value="<?php echo esc_attr($options['texto_botao_aceitar'] ?? 'Sim, eu aceito'); ?>" 
                                       class="termo-input">
                            </label>
                        </div>
                        <div class="termo-input-card">
                            <label>
                                <span class="termo-label-text">Botão Sair</span>
                                <input type="text" 
                                       name="texto_botao_sair" 
                                       value="<?php echo esc_attr($options['texto_botao_sair'] ?? 'Sair'); ?>" 
                                       class="termo-input">
                            </label>
                        </div>
                    </div>
                </div>
            </div>

            <div class="termo-actions">
                <?php submit_button('Salvar Configurações', 'primary termo-btn termo-btn-primary', 'save_termo'); ?>
            </div>
        </form>
    </div>
</div>