/* Variáveis e Te<PERSON> */
:root {
    --primary-color: #4f46e5;
    --primary-hover: #4338ca;
    --danger-color: #ef4444;
    --danger-hover: #dc2626;
    --success-color: #10b981;
    --warning-color: #f59e0b;
    --text-color: #1f2937;
    --text-light: #374151;
    --bg-color: #ffffff;
    --bg-light: #f8fafc;
    --bg-lighter: #f1f5f9;
    --border-color: #e5e7eb;
    --shadow-sm: 0 1px 2px rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.1);
    --radius-sm: 6px;
    --radius-md: 8px;
    --radius-lg: 12px;
    --termo-backdrop-z: 999990;
    --termo-overlay-z: 999991;
    --termo-loading-z: 999992;
    --termo-swal-z: 999999;
}

/* Reset e Base - Admin */
.termo-uso-admin {
    margin: 24px;
    color: var(--text-color);
}

.termo-titulo {
    font-size: 1.875rem;
    font-weight: 600;
    color: var(--text-color);
    margin-bottom: 2rem;
    padding-bottom: 0.5rem;
    border-bottom: 2px solid var(--border-color);
}

.termo-uso-alert {
    display: flex; /* Alinha ícone e texto horizontalmente */
    align-items: center; /* Centraliza verticalmente */
    margin-top: 5px; /* Menor espaçamento acima */
    padding: 8px 10px; /* Espaçamento interno */
    background-color: #ffe5e5; /* Vermelho bem claro */
    border: 1px solid #ffcccc; /* Borda vermelho suave */
    border-radius: 4px; /* Borda levemente arredondada */
    color: #cc0000; /* Texto vermelho escuro */
    font-size: 14px;
    font-weight: bold;
    line-height: 1.2; /* Menor espaçamento entre linhas */
    margin-bottom: 10px;
}

.termo-uso-alert i {
    margin-right: 8px; /* Espaço entre o ícone e o texto */
    font-size: 18px; /* Tamanho do ícone */
    color: #cc0000; /* Mesma cor do texto */
    vertical-align: baseline; /* Alinha o ícone ao texto */
}

.termo-uso-alert p {
    margin: 0; /* Remove margens padrão do texto */
    padding: 0; /* Remove espaçamento interno do texto */
}



/* Cards e Seções */
.termo-card,
.termo-section {
    background: var(--bg-color);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
    padding: 1rem;
    margin-bottom: 1rem;
}

.termo-buttons-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-top: 1rem;
}

.termo-input-card {
    background: var(--bg-light);
    padding: 1.25rem;
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-sm);
    transition: transform 0.2s, box-shadow 0.2s;
}

.termo-input-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.termo-input {
    width: 100%;
    padding: 0.75rem;
    border: 2px solid var(--border-color);
    border-radius: var(--radius-sm);
    font-size: 0.95rem;
    transition: border-color 0.2s, box-shadow 0.2s;
}

.termo-input:focus {
    border-color: var(--primary-color);
    outline: none;
    box-shadow: 0 0 0 3px rgba(79, 70, 229, 0.1);
}


/* Headers das Seções */
.termo-section-header,
.termo-subsection-header {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-top: 1.0rem;
    margin-bottom: 1.0rem;
}

.termo-section-header svg,
.termo-subsection-header svg {
    color: var(--primary-color);
    flex-shrink: 0;
}

.termo-section-title {
    font-size: 1.25rem;
    font-weight: 600;
    color: var(--text-color);
    margin: 0;
    padding-bottom: 0.5rem;
}

.termo-subsection-header {
    border-bottom: 2px solid var(--border-color);
    padding-bottom: 0.75rem;
}

.termo-subsection-title {
    margin: 0;
    font-size: 1.1rem;
    font-weight: 500;
    color: var(--text-color);
}

/* Toggle Switch */
.termo-toggle-label {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 2rem;
    padding: 1rem;
    background: var(--bg-light);
    border-radius: var(--radius-md);
}

.termo-toggle {
    position: relative;
    display: inline-block;
    width: 50px;
    height: 24px;
}

.termo-toggle input {
    opacity: 0;
    width: 0;
    height: 0;
}

.termo-toggle-slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #cbd5e1;
    transition: .3s;
    border-radius: 34px;
}

.termo-toggle-slider:before {
    position: absolute;
    content: "";
    height: 20px;
    width: 20px;
    left: 2px;
    bottom: 2px;
    background-color: white;
    transition: .3s;
    border-radius: 50%;
}

.termo-toggle input:checked + .termo-toggle-slider {
    background-color: var(--primary-color);
}

.termo-toggle input:checked + .termo-toggle-slider:before {
    transform: translateX(26px);
}

/* Editor */
.termo-editor-container {
    margin-bottom: 2rem;
}

.termo-input-versao-termo {
    background: var(--bg-light);
    padding: 1.25rem;
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-sm);
    transition: transform 0.2s, box-shadow 0.2s;
    margin: 1rem 0 2rem;
}

.termo-input-versao-termo label {
    font-weight: bold;
    display: block;
    margin-bottom: 0.5rem;
}

.termo-input-versao-termo input {
    display: block;
    width: 100%;
    padding: 0.5rem;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
    font-size: 1rem;
    margin-bottom: 0.5rem;
}

.termo-input-versao-termo .description {
    font-size: 0.875rem;
    color: var(--text-light);
    margin-top: 0.25rem;
}

/* Grid de Cores */
.termo-colors-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-top: 1rem;
}

.termo-color-card {
    background: var(--bg-light);
    padding: 1.25rem;
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-sm);
    transition: transform 0.2s;
}

.termo-color-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.termo-color-input {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-top: 0.75rem;
}

.termo-color-input input[type="color"] {
    width: 60px;
    height: 40px;
    padding: 0;
    border: 2px solid var(--border-color);
    border-radius: var(--radius-sm);
    cursor: pointer;
    transition: border-color 0.2s;
}

.termo-color-input input[type="color"]:hover {
    border-color: var(--primary-color);
}

/* Frontend - Container Principal */
.termo-uso-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.8);
    z-index: var(--termo-overlay-z);
    display: flex;
    align-items: center;
    justify-content: center;
}

.termo-uso-container {
    background-color: var(--cor-fundo, var(--bg-color));
    color: var(--cor-texto, var(--text-color));
    border-radius: var(--radius-lg);
    padding: 2rem;
    width: 90%;
    max-width: 800px;
    max-height: 90vh;
    position: relative;
    box-shadow: var(--shadow-md);
}

/* Header */
.termo-uso-header h2 {
    color: var(--cor-texto, var(--text-color));
    font-size: 1.5rem;
    font-weight: 600;
}

/* Conteúdo */
.termo-uso-conteudo {
    color: var(--cor-texto, var(--text-color));
    background: var(--bg-light);
    max-height: calc(70vh - 200px);
    overflow-y: auto;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-md);
}

/* Botões */
.termo-uso-botoes {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    margin-top: 1.5rem;
}

.termo-btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: var(--radius-sm);
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    font-size: 1rem;
}

/* Botão Primário (Aceitar) */
.termo-btn-primary {
    background-color: var(--cor-botao-aceitar, var(--primary-color));
    color: white !important;
}

.termo-btn-primary:hover:not(.disabled) {
    background-color: color-mix(in srgb, var(--cor-botao-aceitar, var(--primary-color)) 85%, black);
    color: white !important;
    transform: translateY(-1px);
}

.termo-btn-primary:active:not(.disabled) {
    background-color: color-mix(in srgb, var(--cor-botao-aceitar, var(--primary-color)) 75%, black);
    transform: translateY(0);
}

.termo-btn-primary.disabled {
    opacity: 0.6;
    cursor: not-allowed;
    pointer-events: none;
}

/* Botão de Perigo (Sair) */
.termo-btn-danger {
    background-color: var(--cor-botao-sair, var(--danger-color));
    color: white !important;
}

.termo-btn-danger:hover {
    background-color: color-mix(in srgb, var(--cor-botao-sair, var(--danger-color)) 85%, black);
    color: white !important;
    transform: translateY(-1px);
}

.termo-btn-danger:active {
    background-color: color-mix(in srgb, var(--cor-botao-sair, var(--danger-color)) 75%, black);
    transform: translateY(0);
}

/* SweetAlert2 Customização */
.swal2-container {
    z-index: var(--termo-swal-z) !important;
}

.swal2-popup button.swal2-confirm.swal2-styled {
    background-color: var(--cor-botao-aceitar, var(--primary-color)) !important;
    color: white !important;
}

.swal2-popup button.swal2-confirm.swal2-styled:hover {
    background-color: color-mix(in srgb, var(--cor-botao-aceitar, var(--primary-color)) 85%, black) !important;
    color: white !important;
}

.swal2-popup button.swal2-confirm.swal2-styled:active {
    background-color: color-mix(in srgb, var(--cor-botao-aceitar, var(--primary-color)) 75%, black) !important;
}

.swal2-popup button.swal2-cancel.swal2-styled {
    background-color: var(--cor-botao-sair, var(--danger-color)) !important;
    color: white !important;
}

.swal2-popup button.swal2-cancel.swal2-styled:hover {
    background-color: color-mix(in srgb, var(--cor-botao-sair, var(--danger-color)) 85%, black) !important;
    color: white !important;
}

.swal2-popup button.swal2-cancel.swal2-styled:active {
    background-color: color-mix(in srgb, var(--cor-botao-sair, var(--danger-color)) 75%, black) !important;
}

/* Tabela de Registros */
.termo-table-container {
    overflow-x: auto;
    background: var(--bg-color);
    border-radius: var(--radius-md);
    box-shadow: var(--shadow-sm);
}

.termo-table {
    width: 100%;
    border-collapse: separate;
    border-spacing: 0;
    text-align: left;
}

.termo-table th {
    background: var(--bg-light);
    padding: 1rem;
    font-weight: 600;
    color: var(--text-color);
    border-bottom: 2px solid var(--border-color);
}

.termo-table td {
    padding: 1rem;
    border-bottom: 1px solid var(--border-color);
}

.termo-table tr:hover td {
    background-color: var(--bg-lighter);
}

.termo-table tr:last-child td {
    border-bottom: none;
}

/* Loading */
.termo-uso-loading,
.termo-uso-success {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    padding: 1.5rem 2rem;
    border-radius: var(--radius-md);
    background: var(--bg-color);
    box-shadow: var(--shadow-md);
    display: none;
    z-index: var(--termo-loading-z);
    align-items: center;
}

.termo-loading-spinner {
    width: 24px;
    height: 24px;
    border: 3px solid var(--bg-lighter);
    border-top: 3px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Formulário de Filtro */
.termo-filter-form {
    display: flex;
    align-items: center;
    gap: 10px;
    background: var(--bg-light);
    border: 1px solid var(--border-color);
    padding: 10px;
    border-radius: var(--radius-md);
    margin-bottom: 20px;
}

.termo-filter-form input[type="text"] {
    flex: 1;
    padding: 8px 12px;
    border: 1px solid var(--border-color);
    border-radius: var(--radius-sm);
    font-size: 14px;
    color: var(--text-color);
    background-color: var(--bg-color);
    transition: border-color 0.2s;
}

.termo-filter-form input[type="text"]:focus {
    border-color: var(--primary-color);
    outline: none;
}

.termo-filter-form button {
    padding: 8px 16px;
    background-color: var(--primary-color);
    color: white;
    border: none;
    border-radius: var(--radius-sm);
    font-size: 14px;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
}

.termo-filter-form button:hover {
    background-color: var(--primary-hover);
    transform: translateY(-1px);
}

.termo-filter-form button:active {
    background-color: color-mix(in srgb, var(--primary-color) 75%, black);
    transform: translateY(0);
}
    
    
    
    
border-radius: var(--radius-sm);
    font-size: 14px;
    font-weight: bold;
    cursor: pointer;
    transition: background-color 0.2s;
}

.termo-filter-form button:hover {
    background-color: var(--primary-hover);
}

/* Badges */
.badge {
    padding: 0.25rem 0.75rem;
    border-radius: var(--radius-sm);
    font-size: 0.875rem;
    font-weight: 500;
    display: inline-block;
}

.badge-warning {
    background-color: #fef3c7;
    color: #92400e;
}

/* Paginação */
.termo-paginacao {
    margin-top: 2rem;
    display: flex;
    justify-content: center;
    gap: 0.5rem;
}

.termo-paginacao a,
.termo-paginacao span {
    padding: 0.5rem 1rem;
    border-radius: var(--radius-sm);
    background: var(--bg-light);
    color: var(--text-color);
    text-decoration: none;
    transition: all 0.2s;
}

.termo-paginacao .current {
    background-color: var(--primary-color);
    color: white;
}

.termo-paginacao a:hover {
    background-color: var(--bg-lighter);
    transform: translateY(-1px);
}

/* Backdrop */
.termo-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: var(--termo-backdrop-z);
    display: none;
}

/* Success State */
.termo-uso-success {
    display: none;
    align-items: center;
    gap: 1rem;
}

.termo-success-icon {
    color: var(--success-color);
    font-size: 1.5rem;
}

/* Responsividade */
@media (max-width: 768px) {
    .termo-uso-admin {
        margin: 16px;
    }

    .termo-section,
    .termo-card {
        padding: 1.25rem;
    }

    .termo-colors-grid,
    .termo-buttons-grid {
        grid-template-columns: 1fr;
    }

    .termo-btn {
        width: 100%;
        margin-bottom: 0.5rem;
    }

    .termo-table th,
    .termo-table td {
        padding: 0.75rem;
    }

    .termo-uso-container {
        width: 95%;
        padding: 1rem;
    }

    .termo-filter-form {
        flex-direction: column;
    }

    .termo-filter-form input[type="text"],
    .termo-filter-form button {
        width: 100%;
    }

    .termo-uso-botoes {
        flex-direction: column;
        gap: 0.5rem;
    }

    .termo-paginacao {
        flex-wrap: wrap;
    }
}

/* Shortcodes */
.termo-shortcodes-info {
    background: var(--bg-light);
    border-radius: var(--radius-md);
    padding: 1.5rem;
    margin: 2rem 0;
    box-shadow: var(--shadow-sm);
}

.termo-shortcodes-header {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    margin-bottom: 1.5rem;
}

.termo-shortcodes-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.termo-shortcode-card {
    background: var(--bg-color);
    padding: 1rem;
    border-radius: var(--radius-md);
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
    box-shadow: var(--shadow-sm);
    transition: transform 0.2s, box-shadow 0.2s;
}

.termo-shortcode-card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-md);
}

.termo-shortcode-card code {
    background: var(--bg-lighter);
    padding: 0.5rem 0.75rem;
    border-radius: var(--radius-sm);
    font-size: 0.9rem;
    color: var(--primary-color);
    font-family: ui-monospace, monospace;
}

.termo-shortcode-group {
    margin-bottom: 1.5rem;
}

.termo-shortcode-group h5 {
    font-size: 0.9rem;
    font-weight: 600;
    color: var(--text-light);
    margin: 0 0 1rem 0;
    padding-bottom: 0.5rem;
    border-bottom: 1px solid var(--border-color);
}

/* Print Styles */
@media print {
    .termo-uso-overlay {
        position: relative;
        background: none;
    }

    .termo-uso-container {
        box-shadow: none;
        max-height: none;
    }

    .termo-uso-conteudo {
        overflow: visible;
        max-height: none;
    }

    .termo-uso-botoes {
        display: none;
    }
}