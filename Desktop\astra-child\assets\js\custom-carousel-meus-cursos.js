jQuery(document).ready(function ($) {

    // Função para esconder ou mostrar a seção de comentários
    function toggleCommentsSection(enableComments) {


        if (enableComments === 'no') {
            $('.tutor-course-spotlight-comments').hide();

        } else if (enableComments === 'yes') {
            $('.tutor-course-spotlight-comments').show();

        }
    }

    // Aplique a função ao clicar em um curso no carrossel
    $('.trigger-carousel-meus-cursos').on('click', function (e) {
        var enableComments = $(this).data('enable-comments');


        if (enableComments !== undefined) {
            toggleCommentsSection(enableComments); // Atualiza a exibição dos comentários
        }
    });

    // Defina o estado inicial da seção de comentários ao carregar a página
    $(window).on('load', function () {


        // Verifica o estado do primeiro item carregado
        var firstCourseLink = $('.trigger-carousel-meus-cursos').first();
        if (firstCourseLink.length) {
            var enableComments = firstCourseLink.data('enable-comments');
            toggleCommentsSection(enableComments); // Verifique o estado ao carregar a página
        }
    });
});
