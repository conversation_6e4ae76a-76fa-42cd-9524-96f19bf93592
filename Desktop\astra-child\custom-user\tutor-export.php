<?php
if (!defined('ABSPATH')) exit; // Segurança

// Adicione o manipulador de exportação
add_action('admin_init', 'tlms_handle_export');

// Renderizar página de administração
function tlms_export_render_admin_page() {
    ?>
    <div class="wrap">
        <h1>Exportar Alunos TutorLMS</h1>
        <form method="post" action="">
            <?php wp_nonce_field('tlms_export_students', 'tlms_export_nonce'); ?>
            
            <h2>Selecione os Cursos</h2>
            <div class="courses-grid">
            <?php
            $courses = get_posts([
                'post_type' => 'courses',
                'numberposts' => -1,
                'orderby' => 'title',
                'order' => 'ASC'
            ]);
            foreach ($courses as $course) {
                echo '<div class="course-item">';
                echo '<label><input type="checkbox" name="courses[]" value="' . $course->ID . '"> ' . 
                     esc_html($course->post_title) . ' (' . tlms_count_students_in_course($course->ID) . ' alunos)</label>';
                echo '</div>';
            }
            ?>
            </div>
            
            <h2>Selecione as Categorias</h2>
            <div class="courses-grid">
            <?php
            $categories = get_terms([
                'taxonomy' => 'course-category',
                'hide_empty' => false,
                'orderby' => 'name',
                'order' => 'ASC'
            ]);
            foreach ($categories as $category) {
                echo '<div class="course-item">';
                echo '<label><input type="checkbox" name="categories[]" value="' . $category->term_id . '"> ' . 
                     esc_html($category->name) . '</label>';
                echo '</div>';
            }
            ?>
            </div>
            
            <p><input type="submit" name="export_students" class="button button-primary" value="Exportar Alunos"></p>
        </form>
    </div>
    <?php
}

// Função auxiliar para contar alunos em um curso
function tlms_count_students_in_course($course_id) {
    global $wpdb;
    
    return $wpdb->get_var($wpdb->prepare("
        SELECT COUNT(DISTINCT user_id) 
        FROM {$wpdb->usermeta} 
        WHERE meta_key = '_user_courses' 
        AND meta_value REGEXP '[[:<:]]%d[[:>:]]'
    ", $course_id));
}

// Manipular a exportação
function tlms_handle_export() {
    if (!isset($_POST['export_students']) || !check_admin_referer('tlms_export_students', 'tlms_export_nonce')) {
        return;
    }

    $courses = isset($_POST['courses']) ? array_map('intval', $_POST['courses']) : [];
    $categories = isset($_POST['categories']) ? array_map('intval', $_POST['categories']) : [];
    
    // Log dos dados recebidos
    error_log("Cursos selecionados: " . print_r($courses, true));
    error_log("Categorias selecionadas: " . print_r($categories, true));
    
    $students = tlms_get_students_by_filters($courses, $categories);
    
    if (!empty($students)) {
        tlms_generate_csv($students, $courses);
    } else {
        add_action('admin_notices', function() {
            echo '<div class="notice notice-warning is-dismissible">';
            echo '<p>Nenhum aluno encontrado com os filtros selecionados.</p>';
            echo '</div>';
        });
    }
}

function tlms_get_students_by_filters($courses, $categories) {
    global $wpdb;

    // Query única otimizada
    $query = "SELECT DISTINCT 
                u.ID,
                u.user_email,
                um_fn.meta_value as first_name,
                um_ln.meta_value as last_name,
                p.ID as course_id,
                p.post_title as course_name
              FROM {$wpdb->users} u
              LEFT JOIN {$wpdb->usermeta} um_fn ON u.ID = um_fn.user_id AND um_fn.meta_key = 'first_name'
              LEFT JOIN {$wpdb->usermeta} um_ln ON u.ID = um_ln.user_id AND um_ln.meta_key = 'last_name'
              JOIN {$wpdb->usermeta} um_courses ON u.ID = um_courses.user_id AND um_courses.meta_key = '_user_courses'
              JOIN {$wpdb->posts} p ON p.post_type = 'courses'";

    // Adicionar joins para categorias se necessário
    if (!empty($categories)) {
        $query .= " LEFT JOIN {$wpdb->term_relationships} tr ON p.ID = tr.object_id
                   LEFT JOIN {$wpdb->term_taxonomy} tt ON tr.term_taxonomy_id = tt.term_taxonomy_id";
    }

    $where_clauses = [];
    
    // Condições para cursos
    if (!empty($courses)) {
        $course_conditions = [];
        foreach ($courses as $course) {
            $course_conditions[] = $wpdb->prepare("(um_courses.meta_value REGEXP '[[:<:]]%d[[:>:]]' AND p.ID = %d)", $course, $course);
        }
        $where_clauses[] = "(" . implode(" OR ", $course_conditions) . ")";
    }

    // Condições para categorias
    if (!empty($categories)) {
        $categories_string = implode(',', array_map('intval', $categories));
        $where_clauses[] = "tt.term_id IN ($categories_string)";
    }

    if (!empty($where_clauses)) {
        $query .= " WHERE " . implode(' AND ', $where_clauses);
    }

    // Ordenação
    $query .= " ORDER BY u.ID ASC, p.post_title ASC";

    // Log da query
    error_log("Query de exportação: " . $query);

    return $wpdb->get_results($query);
}

function tlms_generate_csv($students, $courses) {
    if (empty($students)) {
        return;
    }

    // Limpar buffers de saída
    while (ob_get_level()) {
        ob_end_clean();
    }

    // Nome do arquivo
    $filename = count($courses) == 1 
        ? 'alunos-' . sanitize_title(get_the_title($courses[0])) . '-' . date('Y-m-d') . '.csv'
        : 'alunos-cursos-' . date('Y-m-d') . '.csv';

    // Headers para download
    header('Content-Type: text/csv; charset=utf-8');
    header('Content-Disposition: attachment; filename="' . $filename . '"');
    header('Pragma: no-cache');
    header('Expires: 0');
    header('Cache-Control: must-revalidate');
    
    $output = fopen('php://output', 'w');
    
    // BOM para UTF-8
    fputs($output, "\xEF\xBB\xBF");
    
    // Cabeçalhos do CSV
    fputcsv($output, [
        'ID do Aluno',
        'Nome',
        'Sobrenome',
        'Email',
        'ID do Curso',
        'Nome do Curso'
    ]);
    
    // Escrever dados
    foreach ($students as $student) {
        fputcsv($output, [
            $student->ID,
            $student->first_name ?: '',
            $student->last_name ?: '',
            $student->user_email,
            $student->course_id,
            $student->course_name
        ]);
    }
    
    fclose($output);
    exit;
}

// Adicionar menu de administração
function tlms_export_add_admin_menu() {
    add_submenu_page(
        'options-general.php',
        'Exportar Alunos TutorLMS',
        'Exportar Alunos',
        'manage_options',
        'tlms-export-students',
        'tlms_export_render_admin_page'
    );
}
add_action('admin_menu', 'tlms_export_add_admin_menu');